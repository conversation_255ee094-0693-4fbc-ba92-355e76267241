#!/bin/bash

echo "Setting up MCP integration for Telegram Bot..."

# Install Python MCP dependency
echo "Installing MCP Python package..."
pip install mcp

# Install Node.js MCP server (sequential-thinking)
echo "Installing sequential-thinking MCP server..."
npm install -g @modelcontextprotocol/server-sequential-thinking

echo "MCP setup complete!"
echo ""
echo "You can now test the integration with:"
echo "python test_mcp.py"
echo ""
echo "To start the bot with MCP support:"
echo "python -m bot.bot"
