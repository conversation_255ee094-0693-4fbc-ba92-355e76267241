#!/usr/bin/env python3
"""
Test script to discover MCP server capabilities and tools.
"""

import asyncio
import logging
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def discover_server_capabilities():
    """Discover what tools and capabilities are available in the sequential-thinking server."""
    
    server_params = StdioServerParameters(
        command="npx",
        args=["-y", "@modelcontextprotocol/server-sequential-thinking"]
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                print("=== MCP Server Discovery ===")
                
                # List available tools
                print("\n--- Available Tools ---")
                tools = await session.list_tools()
                if tools.tools:
                    for tool in tools.tools:
                        print(f"Tool: {tool.name}")
                        print(f"  Description: {tool.description}")
                        if hasattr(tool, 'inputSchema') and tool.inputSchema:
                            print(f"  Input Schema: {tool.inputSchema}")
                        print()
                else:
                    print("No tools available")
                
                # List available resources
                print("\n--- Available Resources ---")
                try:
                    resources = await session.list_resources()
                    if resources.resources:
                        for resource in resources.resources:
                            print(f"Resource: {resource.name}")
                            print(f"  URI: {resource.uri}")
                            print(f"  Description: {resource.description}")
                            print()
                    else:
                        print("No resources available")
                except Exception as e:
                    print(f"Resources not supported or error: {e}")
                
                # List available prompts
                print("\n--- Available Prompts ---")
                try:
                    prompts = await session.list_prompts()
                    if prompts.prompts:
                        for prompt in prompts.prompts:
                            print(f"Prompt: {prompt.name}")
                            print(f"  Description: {prompt.description}")
                            if hasattr(prompt, 'arguments') and prompt.arguments:
                                print(f"  Arguments: {prompt.arguments}")
                            print()
                    else:
                        print("No prompts available")
                except Exception as e:
                    print(f"Prompts not supported or error: {e}")
                
                # Test calling the first available tool if any
                if tools.tools:
                    first_tool = tools.tools[0]
                    print(f"\n--- Testing Tool: {first_tool.name} ---")
                    
                    # Try different argument patterns
                    test_args = [
                        {"query": "What is 2+2?"},
                        {"message": "What is 2+2?"},
                        {"input": "What is 2+2?"},
                        {"text": "What is 2+2?"},
                        {"thought": "I need to think about what 2+2 equals", "nextThoughtNeeded": True, "thoughtNumber": 1, "totalThoughts": 3}
                    ]
                    
                    for i, args in enumerate(test_args):
                        try:
                            print(f"\nTrying argument pattern {i+1}: {args}")
                            result = await session.call_tool(first_tool.name, args)
                            print(f"Success! Result: {result}")
                            break  # Stop on first success
                        except Exception as e:
                            print(f"Failed with args {args}: {e}")
                            continue
                
    except Exception as e:
        logger.error(f"Error discovering server capabilities: {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(discover_server_capabilities())
