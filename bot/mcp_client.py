"""
MCP (Model Context Protocol) Client Manager
Handles connections to MCP servers and manages communication.
"""

import asyncio
import logging
from typing import Dict, Optional, Any, List
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import config

logger = logging.getLogger(__name__)


class MCPClientManager:
    """Manages MCP server connections and communication."""
    
    def __init__(self):
        self.servers_config = getattr(config, 'mcp_servers', {})
        self.active_connections: Dict[str, Any] = {}
        
    async def get_server_for_query(self, query: str) -> Optional[str]:
        """
        Determine which MCP server should handle the query based on trigger keywords.
        Returns server name or None if no server matches.
        """
        query_lower = query.lower()
        
        for server_name, server_config in self.servers_config.items():
            triggers = server_config.get('triggers', [])
            if any(trigger.lower() in query_lower for trigger in triggers):
                logger.info(f"Query matched MCP server: {server_name}")
                return server_name
                
        return None
    
    async def call_mcp_server(self, server_name: str, query: str) -> Optional[str]:
        """
        Call the specified MCP server with the query.
        Returns the response or None if failed.
        """
        if server_name not in self.servers_config:
            logger.error(f"Unknown MCP server: {server_name}")
            return None
            
        try:
            server_config = self.servers_config[server_name]
            
            # Create server parameters
            server_params = StdioServerParameters(
                command=server_config['command'],
                args=server_config['args']
            )
            
            # Connect to MCP server and get response
            async with stdio_client(server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    
                    # List available tools
                    tools = await session.list_tools()
                    
                    if not tools.tools:
                        logger.warning(f"No tools available in MCP server: {server_name}")
                        return None
                    
                    # Use the first available tool for all servers
                    tool_name = tools.tools[0].name

                    # Try different common argument patterns
                    arg_patterns = [
                        {"query": query},
                        {"message": query},
                        {"input": query},
                        {"text": query},
                        {"prompt": query}
                    ]

                    # For sequential thinking, add its specific pattern
                    if tool_name == "sequentialthinking":
                        arg_patterns.insert(0, {
                            "thought": query,
                            "nextThoughtNeeded": False,
                            "thoughtNumber": 1,
                            "totalThoughts": 1
                        })

                    for args in arg_patterns:
                        try:
                            result = await session.call_tool(tool_name, args)
                            if result.content and result.content[0].text:
                                return str(result.content[0].text)
                            break
                        except Exception as e:
                            logger.debug(f"Failed with args {args}: {e}")
                            continue

                    return None
                    
        except Exception as e:
            logger.error(f"Error calling MCP server {server_name}: {e}")
            return None
    



# Global instance
mcp_client = MCPClientManager()
