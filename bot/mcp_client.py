"""
MCP (Model Context Protocol) Client Manager
Handles connections to MCP servers and manages communication.
"""

import asyncio
import logging
from typing import Dict, Optional, Any, List
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import config

logger = logging.getLogger(__name__)


class MCPClientManager:
    """Manages MCP server connections and communication."""
    
    def __init__(self):
        self.servers_config = getattr(config, 'mcp_servers', {})
        self.active_connections: Dict[str, Any] = {}
        
    async def get_server_for_query(self, query: str) -> Optional[str]:
        """
        Determine which MCP server should handle the query based on trigger keywords.
        Returns server name or None if no server matches.
        """
        query_lower = query.lower()
        
        for server_name, server_config in self.servers_config.items():
            triggers = server_config.get('triggers', [])
            if any(trigger.lower() in query_lower for trigger in triggers):
                logger.info(f"Query matched MCP server: {server_name}")
                return server_name
                
        return None
    
    async def call_mcp_server(self, server_name: str, query: str) -> Optional[str]:
        """
        Call the specified MCP server with the query.
        Returns the response or None if failed.
        """
        if server_name not in self.servers_config:
            logger.error(f"Unknown MCP server: {server_name}")
            return None
            
        try:
            server_config = self.servers_config[server_name]
            
            # Create server parameters
            server_params = StdioServerParameters(
                command=server_config['command'],
                args=server_config['args']
            )
            
            # Connect to MCP server and get response
            async with stdio_client(server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    
                    # List available tools
                    tools = await session.list_tools()
                    
                    if not tools.tools:
                        logger.warning(f"No tools available in MCP server: {server_name}")
                        return None
                    
                    # For sequential-thinking server, use the sequential_thinking tool
                    if server_name == "sequential-thinking":
                        return await self._call_sequential_thinking(session, query)
                    
                    # For other servers, use the first available tool
                    tool_name = tools.tools[0].name

                    # Try different common argument patterns
                    arg_patterns = [
                        {"query": query},
                        {"message": query},
                        {"input": query},
                        {"text": query},
                        {"prompt": query}
                    ]

                    for args in arg_patterns:
                        try:
                            result = await session.call_tool(tool_name, args)
                            if result.content and result.content[0].text:
                                return str(result.content[0].text)
                            break
                        except Exception as e:
                            logger.debug(f"Failed with args {args}: {e}")
                            continue

                    return None
                    
        except Exception as e:
            logger.error(f"Error calling MCP server {server_name}: {e}")
            return None
    
    async def _call_sequential_thinking(self, session: ClientSession, query: str) -> Optional[str]:
        """
        Handle sequential thinking server specifically.
        Note: This server is designed to structure thinking for LLMs, not generate content.
        We'll use it to demonstrate structured reasoning about the query.
        """
        try:
            # The sequential thinking server is designed to help structure reasoning,
            # so we'll create a structured response that shows how it would be used

            # Step 1: Initial analysis
            result1 = await session.call_tool("sequentialthinking", {
                "thought": f"Let me analyze this query step by step: {query}",
                "nextThoughtNeeded": True,
                "thoughtNumber": 1,
                "totalThoughts": 3
            })

            # Step 2: Deeper analysis
            result2 = await session.call_tool("sequentialthinking", {
                "thought": f"Now I need to break down the key components and approach for: {query}",
                "nextThoughtNeeded": True,
                "thoughtNumber": 2,
                "totalThoughts": 3
            })

            # Step 3: Final synthesis
            result3 = await session.call_tool("sequentialthinking", {
                "thought": f"Let me synthesize my analysis and provide a structured response to: {query}",
                "nextThoughtNeeded": False,
                "thoughtNumber": 3,
                "totalThoughts": 3
            })

            # Create a structured response showing the thinking process
            response_parts = [
                "🧠 **Sequential Thinking Analysis**",
                "",
                "**Step 1: Initial Analysis**",
                f"• Analyzing query: {query}",
                "• Identifying key components and requirements",
                "",
                "**Step 2: Structured Breakdown**",
                "• Breaking down the problem into manageable parts",
                "• Considering different approaches and perspectives",
                "",
                "**Step 3: Synthesis & Response**",
                "• Integrating insights from previous steps",
                "• Formulating a comprehensive response",
                "",
                "**Note**: This demonstrates how the sequential thinking MCP server structures reasoning processes for AI assistants. In a full implementation, each step would contain detailed analysis specific to your query."
            ]

            return "\n".join(response_parts)

        except Exception as e:
            logger.error(f"Error in sequential thinking: {e}")
            return None


# Global instance
mcp_client = MCPClientManager()
