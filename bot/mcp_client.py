"""
MCP (Model Context Protocol) Client Manager
Handles connections to MCP servers and manages communication.
"""

import asyncio
import logging
from typing import Dict, Optional, Any, List
from mcp.client.session import ClientSession
from mcp.client.stdio import StdioServerParameters, stdio_client
import config

logger = logging.getLogger(__name__)


class MCPClientManager:
    """Manages MCP server connections and communication."""
    
    def __init__(self):
        self.servers_config = getattr(config, 'mcp_servers', {})
        self.active_connections: Dict[str, Any] = {}
        
    async def get_server_for_query(self, query: str) -> Optional[str]:
        """
        Determine which MCP server should handle the query based on trigger keywords.
        Returns server name or None if no server matches.
        """
        query_lower = query.lower()
        
        for server_name, server_config in self.servers_config.items():
            triggers = server_config.get('triggers', [])
            if any(trigger.lower() in query_lower for trigger in triggers):
                logger.info(f"Query matched MCP server: {server_name}")
                return server_name
                
        return None
    
    async def call_mcp_server(self, server_name: str, query: str) -> Optional[str]:
        """
        Call the specified MCP server with the query.
        Returns the response or None if failed.
        """
        if server_name not in self.servers_config:
            logger.error(f"Unknown MCP server: {server_name}")
            return None
            
        try:
            server_config = self.servers_config[server_name]
            
            # Create server parameters
            server_params = StdioServerParameters(
                command=server_config['command'],
                args=server_config['args']
            )
            
            # Connect to MCP server and get response
            async with stdio_client(server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    
                    # List available tools
                    tools = await session.list_tools()
                    
                    if not tools.tools:
                        logger.warning(f"No tools available in MCP server: {server_name}")
                        return None
                    
                    # For sequential-thinking server, use the sequential_thinking tool
                    if server_name == "sequential-thinking":
                        return await self._call_sequential_thinking(session, query)
                    
                    # For other servers, use the first available tool
                    tool_name = tools.tools[0].name
                    result = await session.call_tool(tool_name, {"query": query})
                    
                    if result.content:
                        return str(result.content[0].text if result.content[0].text else "")
                    
                    return None
                    
        except Exception as e:
            logger.error(f"Error calling MCP server {server_name}: {e}")
            return None
    
    async def _call_sequential_thinking(self, session: ClientSession, query: str) -> Optional[str]:
        """Handle sequential thinking server specifically."""
        try:
            # Start with initial thought
            result = await session.call_tool("sequential_thinking", {
                "thought": f"I need to analyze this query: {query}",
                "nextThoughtNeeded": True,
                "thoughtNumber": 1,
                "totalThoughts": 5
            })
            
            thoughts = []
            thought_number = 1
            
            # Continue thinking process
            while thought_number <= 5:
                if result.content and result.content[0].text:
                    thoughts.append(f"Thought {thought_number}: {result.content[0].text}")
                
                thought_number += 1
                if thought_number > 5:
                    break
                    
                # Continue with next thought
                result = await session.call_tool("sequential_thinking", {
                    "thought": f"Building on previous thoughts, let me continue analyzing: {query}",
                    "nextThoughtNeeded": thought_number < 5,
                    "thoughtNumber": thought_number,
                    "totalThoughts": 5
                })
            
            # Combine all thoughts into a coherent response
            if thoughts:
                return "\n\n".join(thoughts)
            
            return None
            
        except Exception as e:
            logger.error(f"Error in sequential thinking: {e}")
            return None


# Global instance
mcp_client = MCPClientManager()
