"""
MCP Request Router
Intelligent routing logic to determine which MCP server should handle a request.
"""

import logging
from typing import Optional
from mcp_client import mcp_client

logger = logging.getLogger(__name__)


async def route_to_mcp(message: str) -> Optional[str]:
    """
    Route a user message to the appropriate MCP server.
    
    Args:
        message: The user's message text
        
    Returns:
        MCP server response if routed to MCP, None if should use OpenAI
    """
    try:
        # Check if any MCP server should handle this query
        server_name = await mcp_client.get_server_for_query(message)
        
        if server_name:
            logger.info(f"Routing message to MCP server: {server_name}")
            response = await mcp_client.call_mcp_server(server_name, message)
            
            if response:
                # Add a subtle indicator that this came from MCP
                server_config = mcp_client.servers_config.get(server_name, {})
                description = server_config.get('description', server_name)
                return f"🧠 *{description}*\n\n{response}"
            else:
                logger.warning(f"MCP server {server_name} returned empty response")
        
        # No MCP server matched or MCP failed, use OpenAI
        return None
        
    except Exception as e:
        logger.error(f"Error in MCP routing: {e}")
        # Fall back to OpenAI on any error
        return None


def should_use_mcp(message: str) -> bool:
    """
    Quick check if message might benefit from MCP processing.
    Used for early filtering before async operations.
    """
    if not message:
        return False
        
    message_lower = message.lower()
    
    # Check for reasoning/thinking keywords
    thinking_keywords = [
        "think", "analyze", "reason", "step by step", "complex problem",
        "reasoning", "logic", "solve", "breakdown", "systematic",
        "explain how", "walk me through", "break down", "step-by-step"
    ]
    
    return any(keyword in message_lower for keyword in thinking_keywords)
