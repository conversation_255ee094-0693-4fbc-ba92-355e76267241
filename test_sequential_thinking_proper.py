#!/usr/bin/env python3
"""
Test script to understand how the sequential thinking server actually works.
"""

import asyncio
import logging
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_sequential_thinking_properly():
    """Test the sequential thinking server with proper usage."""
    
    server_params = StdioServerParameters(
        command="npx",
        args=["-y", "@modelcontextprotocol/server-sequential-thinking"]
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                print("=== Testing Sequential Thinking Server ===\n")
                
                # Test 1: Simple reasoning task
                print("Test 1: Simple Math Problem")
                print("-" * 40)
                
                result = await session.call_tool("sequentialthinking", {
                    "thought": "I need to solve 15 * 24. Let me break this down step by step.",
                    "nextThoughtNeeded": True,
                    "thoughtNumber": 1,
                    "totalThoughts": 3
                })
                
                print(f"Response: {result}")
                if result.content:
                    print(f"Content: {result.content[0].text}")
                
                print("\n" + "="*50 + "\n")
                
                # Test 2: Complex reasoning
                print("Test 2: Complex Problem Solving")
                print("-" * 40)
                
                result = await session.call_tool("sequentialthinking", {
                    "thought": "I need to analyze the pros and cons of renewable energy adoption. Let me think through this systematically.",
                    "nextThoughtNeeded": True,
                    "thoughtNumber": 1,
                    "totalThoughts": 5
                })
                
                print(f"Response: {result}")
                if result.content:
                    print(f"Content: {result.content[0].text}")
                
                print("\n" + "="*50 + "\n")
                
                # Test 3: Try to continue the thinking process
                print("Test 3: Continuing the thought process")
                print("-" * 40)
                
                # First thought
                result1 = await session.call_tool("sequentialthinking", {
                    "thought": "I need to plan a birthday party. Let me start by thinking about the key elements.",
                    "nextThoughtNeeded": True,
                    "thoughtNumber": 1,
                    "totalThoughts": 4
                })
                
                print(f"Thought 1 Response: {result1.content[0].text if result1.content else 'No content'}")
                
                # Second thought
                result2 = await session.call_tool("sequentialthinking", {
                    "thought": "Now I need to consider the budget and venue options for the party.",
                    "nextThoughtNeeded": True,
                    "thoughtNumber": 2,
                    "totalThoughts": 4
                })
                
                print(f"Thought 2 Response: {result2.content[0].text if result2.content else 'No content'}")
                
                # Third thought
                result3 = await session.call_tool("sequentialthinking", {
                    "thought": "Let me think about the guest list and invitations.",
                    "nextThoughtNeeded": True,
                    "thoughtNumber": 3,
                    "totalThoughts": 4
                })
                
                print(f"Thought 3 Response: {result3.content[0].text if result3.content else 'No content'}")
                
                # Final thought
                result4 = await session.call_tool("sequentialthinking", {
                    "thought": "Now let me create a final plan based on all my previous thoughts.",
                    "nextThoughtNeeded": False,
                    "thoughtNumber": 4,
                    "totalThoughts": 4
                })
                
                print(f"Final Thought Response: {result4.content[0].text if result4.content else 'No content'}")
                
    except Exception as e:
        logger.error(f"Error testing sequential thinking: {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(test_sequential_thinking_properly())
