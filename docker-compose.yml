version: "3"

services:
  mongo:
    container_name: ${CHAT_GPT_MONGO_CONTAINER_NAME:-mongo}
    image: mongo:latest
    restart: always
    command: mongod --bind_ip_all --port ${MONGODB_PORT:-27017}
    ports:
      - 127.0.0.1:${MONGODB_PORT:-27017}:${MONGODB_PORT:-27017}
    volumes:
      - ${MONGODB_PATH:-./mongodb}:/data/db
    # TODO: add auth

  chatgpt_telegram_bot:
    container_name: ${CHAT_GPT_CONTAINER_NAME:-chatgpt_telegram_bot}
    command: python3 bot/bot.py
    restart: always
    build:
      context: "."
      dockerfile: Dockerfile
    depends_on:
      - mongo

  mongo_express:
    container_name: ${CHAT_GPT_MONGO_EXPRESS_CONTAINER_NAME:-mongo-express}
    image: mongo-express:latest
    restart: always
    ports:
      - 127.0.0.1:${MONGO_EXPRESS_PORT:-8081}:${MONGO_EXPRESS_PORT:-8081}
    environment:
      - ME_CONFIG_MONGODB_SERVER=${CHAT_GPT_MONGO_CONTAINER_NAME:-mongo}
      - ME_CONFIG_MONGODB_PORT=${MONGODB_PORT:-27017}
      - ME_CONFIG_MONGODB_ENABLE_ADMIN=false
      - ME_CONFIG_MONGODB_AUTH_DATABASE=chatgpt_telegram_bot
      - ME_CONFIG_BASICAUTH_USERNAME=${MONGO_EXPRESS_USERNAME:-username}
      - ME_CONFIG_BASICAUTH_PASSWORD=${MONGO_EXPRESS_PASSWORD:-password}
    depends_on:
      - mongo
