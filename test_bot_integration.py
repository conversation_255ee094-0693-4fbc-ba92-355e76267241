#!/usr/bin/env python3
"""
Final integration test to verify the bot works with MCP integration.
"""

import asyncio
import logging
import sys
import os

# Add the bot directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'bot'))

from mcp_router import route_to_mcp, should_use_mcp

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_mcp_routing():
    """Test the MCP routing logic directly."""

    test_messages = [
        {
            "message": "Think step by step about how to solve a complex math problem",
            "should_use_mcp": True,
            "description": "Complex reasoning query - should trigger MCP"
        },
        {
            "message": "Help me analyze this problem systematically",
            "should_use_mcp": True,
            "description": "Analysis query - should trigger MCP"
        },
        {
            "message": "What is the weather like today?",
            "should_use_mcp": False,
            "description": "Simple query - should use OpenAI fallback"
        },
        {
            "message": "I need to reason through this step by step",
            "should_use_mcp": True,
            "description": "Reasoning query - should trigger MCP"
        },
        {
            "message": "Break down this complex problem for me",
            "should_use_mcp": True,
            "description": "Breakdown query - should trigger MCP"
        }
    ]

    print("=== Testing MCP Routing Logic ===\n")

    for i, test_case in enumerate(test_messages, 1):
        print(f"Test {i}: {test_case['description']}")
        print(f"Message: '{test_case['message']}'")
        print(f"Expected MCP usage: {test_case['should_use_mcp']}")
        print("-" * 60)

        try:
            # Test should_use_mcp function
            should_use = should_use_mcp(test_case['message'])
            print(f"should_use_mcp result: {should_use}")

            if should_use == test_case['should_use_mcp']:
                print("✅ MCP detection matches expectation")
            else:
                print("❌ MCP detection doesn't match expectation")

            # If should use MCP, test the routing
            if should_use:
                try:
                    response = await route_to_mcp(test_case['message'])
                    if response:
                        print(f"✅ MCP response received: {len(response)} characters")
                        print(f"Response preview: {response[:150]}...")
                    else:
                        print("❌ No MCP response received")
                except Exception as e:
                    print(f"❌ MCP routing error: {e}")
            else:
                print("⏭️  Skipping MCP routing (not expected to use MCP)")

        except Exception as e:
            print(f"❌ Error: {e}")
            logger.error(f"Error in test {i}: {e}")

        print("\n" + "="*70 + "\n")


async def test_mcp_configuration():
    """Test that MCP configuration is properly loaded."""
    
    print("=== Testing MCP Configuration ===\n")
    
    try:
        import config
        
        print(f"MCP servers configuration: {config.mcp_servers}")
        
        if config.mcp_servers:
            print("✅ MCP servers configuration loaded successfully")
            
            for server_name, server_config in config.mcp_servers.items():
                print(f"  - {server_name}: {server_config}")
        else:
            print("❌ No MCP servers configuration found")
            
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")


if __name__ == "__main__":
    print("Starting Bot Integration Tests...\n")
    
    # Test configuration first
    asyncio.run(test_mcp_configuration())
    print("\n" + "="*70 + "\n")
    
    # Test MCP routing
    asyncio.run(test_mcp_routing())
    
    print("Integration tests completed!")
