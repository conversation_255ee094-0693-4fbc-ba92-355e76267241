telegram_token: ""
openai_api_key: ""
openai_api_base: null  # leave null to use default api base or you can put your own base url here
allowed_telegram_usernames: []  # if empty, the bot is available to anyone. pass a username string to allow it and/or user ids as positive integers and/or channel ids as negative integers
new_dialog_timeout: 600  # new dialog starts after timeout (in seconds)
return_n_generated_images: 1
n_chat_modes_per_page: 5
image_size: "512x512" # the image size for image generation. Generated images can have a size of 256x256, 512x512, or 1024x1024 pixels. Smaller sizes are faster to generate.
enabled_chat_models: ["assistant", "code_assistant", "expert_code_assistant", "artist", "money_maker", "travel_guide", "movie_expert"]

# prices
chatgpt_price_per_1000_tokens: 0.002
gpt_price_per_1000_tokens: 0.02
whisper_price_per_1_min: 0.006
