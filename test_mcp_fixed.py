#!/usr/bin/env python3
"""
Test script for the fixed MCP implementation.
"""

import asyncio
import logging
import sys
import os

# Add the bot directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'bot'))

from mcp_client import mcp_client
from mcp_router import route_to_mcp, should_use_mcp

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_mcp_integration():
    """Test the MCP integration with various queries."""
    
    test_queries = [
        "Think step by step about how to solve 2+2",
        "Analyze the problem of climate change systematically",
        "I need to reason through this complex issue",
        "Help me break down this problem step by step",
        "What is the capital of France?",  # Should not trigger MCP
    ]
    
    print("=== Testing MCP Integration ===\n")
    
    for i, query in enumerate(test_queries, 1):
        print(f"Test {i}: {query}")
        print("-" * 50)
        
        # Test if query should use MCP
        should_use = should_use_mcp(query)
        print(f"Should use MCP: {should_use}")
        
        if should_use:
            # Test server selection
            server_name = await mcp_client.get_server_for_query(query)
            print(f"Selected server: {server_name}")
            
            if server_name:
                # Test MCP routing
                try:
                    response = await route_to_mcp(query)
                    if response:
                        print(f"MCP Response:\n{response}")
                    else:
                        print("No response from MCP")
                except Exception as e:
                    print(f"Error: {e}")
            else:
                print("No server matched the query")
        else:
            print("Query would use OpenAI fallback")
        
        print("\n" + "="*60 + "\n")


async def test_direct_sequential_thinking():
    """Test the sequential thinking server directly."""
    
    print("=== Testing Sequential Thinking Server Directly ===\n")
    
    query = "Think through the steps to bake a chocolate cake"
    
    try:
        response = await mcp_client.call_mcp_server("sequential-thinking", query)
        if response:
            print(f"Direct Sequential Thinking Response:\n{response}")
        else:
            print("No response from sequential thinking server")
    except Exception as e:
        print(f"Error calling sequential thinking server: {e}")


if __name__ == "__main__":
    print("Starting MCP Integration Tests...\n")
    
    # Run the tests
    asyncio.run(test_mcp_integration())
    print("\n" + "="*60 + "\n")
    asyncio.run(test_direct_sequential_thinking())
    
    print("\nTests completed!")
